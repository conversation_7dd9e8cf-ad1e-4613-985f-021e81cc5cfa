# SmartSecHub Resource Extraction Instructions for ChatGPT

## Overview
You are tasked with analyzing cybersecurity-related URLs and extracting structured information to create JSON resources for SmartSecHub. Follow these instructions carefully to generate properly formatted JSON data.

## Your Task
1. Visit each provided URL
2. Read and analyze the content
3. Extract relevant information
4. Generate a JSON array of resources following the specified schema

## JSON Schema Requirements

Each resource object must contain the following fields:

### Required Fields:
- **title** (string): The main title of the content
- **url** (string): The original URL provided
- **type** (string): One of the following values:
  - `"article"` - Blog posts, articles, written content
  - `"video"` - YouTube videos, video tutorials
  - `"tweet"` - Twitter/X posts
  - `"github"` - GitHub repositories, code projects
  - `"papers"` - Research papers, whitepapers, academic content
  - `"sites"` - Websites, tools, platforms

### Optional Fields:
- **description** (string): A concise summary of the content (2-3 sentences max)
- **tags** (array): Leave as empty array `[]` - will be filled manually later

### Fields NOT to include:
- Do not include `authorId` field
- Do not include `id`, `likesCount`, `dislikesCount`, `createdAt`, or other metadata

## Content Analysis Guidelines

### Title Extraction:
- Use the actual title from the webpage/content
- If no clear title exists, create a descriptive one
- Keep titles concise but informative
- Remove unnecessary prefixes like "Blog:" or suffixes like "| Company Name"

### Type Classification:
- **article**: Blog posts, tutorials, guides, news articles, documentation
- **video**: Any video content (YouTube, Vimeo, etc.)
- **tweet**: Twitter/X posts and threads
- **github**: GitHub repositories, code samples, open source projects
- **papers**: Academic papers, research documents, whitepapers, technical reports
- **sites**: Tools, platforms, websites, online services, dashboards

### Description Guidelines:
- Write 2-3 sentences maximum
- Focus on what the content teaches or provides
- Mention key topics, techniques, or tools covered
- Use clear, professional language
- Avoid marketing language or excessive adjectives

## Output Format

Return a valid JSON array with this exact structure:

```json
[
  {
    "title": "Example Security Article Title",
    "url": "https://example.com/article",
    "description": "A comprehensive guide covering web application security fundamentals. Explains common vulnerabilities like XSS and SQL injection with practical examples.",
    "type": "article",
    "tags": []
  },
  {
    "title": "Another Resource Title",
    "url": "https://example.com/video",
    "description": "Video tutorial demonstrating penetration testing techniques using popular tools.",
    "type": "video",
    "tags": []
  }
]
```

## Quality Standards

### Content Relevance:
Only include content related to:
- Cybersecurity
- Information security
- Penetration testing
- Vulnerability research
- Security tools and techniques
- Incident response
- Security architecture
- Compliance and governance
- Privacy and data protection

### Accuracy Requirements:
- Verify the URL is accessible
- Ensure the title matches the actual content
- Confirm the type classification is correct
- Write descriptions based on actual content, not assumptions

## Error Handling

If a URL is inaccessible or contains irrelevant content:
- Skip that URL
- Do not include it in the JSON output
- Mention skipped URLs in a separate note after the JSON

## Example Input/Output

**Input URLs:**
- https://owasp.org/www-project-top-ten/
- https://github.com/danielmiessler/SecLists

**Expected Output:**
```json
[
  {
    "title": "OWASP Top 10 Web Application Security Risks",
    "url": "https://owasp.org/www-project-top-ten/",
    "description": "The OWASP Top 10 is a standard awareness document for developers and web application security. It represents a broad consensus about the most critical security risks to web applications.",
    "type": "sites",
    "tags": []
  },
  {
    "title": "SecLists - Security Testing Lists",
    "url": "https://github.com/danielmiessler/SecLists",
    "description": "Collection of multiple types of lists used during security assessments. Includes usernames, passwords, URLs, sensitive data patterns, fuzzing payloads, web shells, and more.",
    "type": "github",
    "tags": []
  }
]
```

## Final Notes

- Always return valid JSON that can be parsed
- Double-check URLs for typos
- Ensure all required fields are present
- Keep descriptions factual and informative
- Focus on cybersecurity relevance
- Maintain consistent formatting

---

**Instructions for use:** Paste the URLs you want to analyze after this prompt, and I will generate the JSON array following these specifications.
