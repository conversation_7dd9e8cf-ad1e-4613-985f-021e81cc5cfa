import { AppResource, AppTag } from '../../core.types';

import { <PERSON>, CardContent, CardFooter } from '@/components/ui/card';
import c from 'classnames';
import { Tweet } from 'react-tweet';
import { useLocalStorage } from 'usehooks-ts';
import { useRootContext } from '../../context/useRootContext';
import { BookmarkAction } from '../resource-actions/bookmark-action';
import { DeleteAction } from '../resource-actions/delete-action';
import { DislikeAction } from '../resource-actions/dislike-action';
import { EditAction } from '../resource-actions/edit-action';
import { LikeAction } from '../resource-actions/like-action';
import { ReadMarkAction } from '../resource-actions/readmark-action';
import { ResourceTags } from './resource-tags';

type TweetTileProps = {
  resource: AppResource;
};

export const TweetCard = ({ resource }: TweetTileProps) => {
  const { tags } = useRootContext();
  const [isDarkMode] = useLocalStorage<boolean>('usehooks-ts-dark-mode', false);

  const tagsList = resource.tags
    .map((tagId) => tags.find((tag) => tag.id === tagId))
    .filter(Boolean) as AppTag[];

  const tweetId = resource.url.split('/').pop();

  return (
    <Card className='flex flex-col'>
      <div className='flex justify-end f-full pt-2'>
        <LikeAction {...{ resource }} />
        <DislikeAction {...{ resource }} />
        <BookmarkAction appResourceId={resource.id} />
        <ReadMarkAction appResourceId={resource.id} />
      </div>
      <CardContent>
        <div
          className={c('flex justify-center', {
            dark: isDarkMode,
            light: !isDarkMode,
          })}
        >
          <Tweet id={tweetId as string} />
        </div>
        <div className='flex justify-end gap-2 py-2 w-full'>
          <EditAction {...{ resource }} />
          <DeleteAction resourceId={resource.id} />
        </div>
      </CardContent>
      <CardFooter className='grow'>
        <ResourceTags tags={tagsList} className='mt-auto' />
      </CardFooter>
    </Card>
  );
};
