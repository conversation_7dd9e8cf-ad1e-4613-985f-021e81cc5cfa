import { SubmitHandler } from 'react-hook-form';

import { createAppResource } from '../../api';
import { AppResource } from '../../core.types';
import { ManageAppResourceForm } from './ManageAppResourceForm';

export const ManageAppResources = () => {
  const onSubmit: SubmitHandler<AppResource> = async (values) => {
    await createAppResource(values);
  };

  return (
    <div className='flex gap-2 w-[750px] mx-auto mt-4'>
      <ManageAppResourceForm onSubmit={onSubmit} />
    </div>
  );
};
