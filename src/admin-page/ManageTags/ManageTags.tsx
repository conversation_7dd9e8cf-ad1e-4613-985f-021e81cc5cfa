import { groupTags } from '@/services/utils-service';
import { useCallback } from 'react';
import { toast } from 'react-toastify';
import { createTag } from '../../api';
import { useRootContext } from '../../context/useRootContext';
import { AppTag, TagGroupName } from '../../core.types';
import { ManageTagForm } from './ManageTagForm';
import { ManageTagsItem } from './ManageTagsItem';

export const ManageTags = () => {
  const { tags, setTags } = useRootContext();

  const onTagCreate = useCallback(
    async ({ group, name }: AppTag) => {
      const isAlreadyAdded = tags.find((tag) => tag.name === name);
      if (isAlreadyAdded) {
        toast.error('Tag is already added');
        return;
      }

      const tagId = await createTag({ group, name } as any);
      setTags((prevTags: AppTag[]) => [
        ...prevTags,
        { group, name, id: tagId },
      ]);
    },
    [setTags, tags],
  );

  const groupedTags = groupTags(tags);

  return (
    <div className='flex gap-3 m-4'>
      <div className='w-[27%]'>
        <ManageTagForm onSubmit={onTagCreate} />
      </div>
      <div className='flex flex-wrap'>
        {Object.entries(groupedTags).map(([group, tagsInGroup]) => (
          <div key={group} className='p-4 m-2 bg-[#e3e4ea] rounded'>
            <h3 className='font-bold text-lg text-black'>
              {TagGroupName[group]}
            </h3>
            <div className='mt-2 gap-1'>
              {(tagsInGroup as AppTag[]).map((tag) => (
                <ManageTagsItem key={tag.id} tag={tag} />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
