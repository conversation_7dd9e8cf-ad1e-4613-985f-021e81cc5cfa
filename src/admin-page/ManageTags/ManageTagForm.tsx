import { yup<PERSON><PERSON>olver } from '@hookform/resolvers/yup';
import { useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, SubmitHandler, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { AppInput } from '../../components/app-input/app-input';
import { AppSelect } from '../../components/AppSelect/AppSelect';
import { AppSubmitButton } from '../../components/buttons/AppSubmitButton';
import { AppTag, TagGroupSelectOptions } from '../../core.types';

const tagSchema = yup.object({
  name: yup.string().min(1, 'Name is required').required(),
  group: yup.string().required(),
});

export const ManageTagForm = ({
  onSubmit,
  defaultValues,
}: {
  onSubmit: SubmitHandler<AppTag>;
  defaultValues?: AppTag;
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors: errorsTag, isSubmitting },
    setValue,
    reset,
    watch,
  } = useForm<AppTag>({
    resolver: yupResolver<AppTag>(tagSchema as any),
    defaultValues,
  });

  const groupValue = watch('group');

  useEffect(() => {
    register('group');
  }, [register]);

  const onGroupChange = (value) => {
    setValue('group', value);
  };

  return (
    <FormProvider {...({ register } as any)}>
      <form
        onSubmit={handleSubmit((...rest) => {
          onSubmit(...rest);
          // @ts-expect-error note
          setValue('group', null);
          reset();
        })}
        className='flex flex-col gap-2 w-[400px] mx-auto'
      >
        <AppInput
          name='name'
          placeholder='Name'
          type='text'
          error={errorsTag?.name?.message}
        />
        <AppSelect
          placeholder='Select Group'
          value={groupValue}
          options={TagGroupSelectOptions}
          onChange={onGroupChange}
        />
        <AppSubmitButton isSubmitting={isSubmitting} />
      </form>
    </FormProvider>
  );
};
