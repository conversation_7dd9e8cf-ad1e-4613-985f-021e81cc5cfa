import { AppHeader } from '@/components/app-header/app-header';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import { createAppResource } from '../api';
import { AppSubmitButton } from '../components/buttons/AppSubmitButton';
import { AppResource } from '../core.types';
import {
  BulkResourceArrayData,
  bulkResourceArraySchema,
} from '../schemas/app-resource.schema';
import { AdminNavigation } from './AdminNavigation';
import { AppTextarea } from '@/components/app-input/app-textarea';

export const AdminBulkImportPage = () => {
  const [jsonInput, setJsonInput] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = useForm();

  const validateAndParseJson = (
    jsonString: string,
  ): BulkResourceArrayData | null => {
    try {
      const parsed = JSON.parse(jsonString);
      const validated = bulkResourceArraySchema.parse(parsed);
      setValidationErrors([]);
      return validated;
    } catch (error: any) {
      if (error.errors) {
        // Zod validation errors
        const errorMessages = error.errors.map(
          (err: any) => `${err.path.join('.')}: ${err.message}`,
        );
        setValidationErrors(errorMessages);
      } else {
        // JSON parsing errors
        setValidationErrors([error.message]);
      }
      return null;
    }
  };

  const onSubmit = async () => {
    if (!jsonInput.trim()) {
      toast.error('Please enter JSON data');
      return;
    }

    const validatedData = validateAndParseJson(jsonInput);
    if (!validatedData) {
      toast.error('Please fix validation errors before submitting');
      return;
    }

    setIsProcessing(true);
    let successCount = 0;
    let errorCount = 0;

    try {
      for (const resource of validatedData) {
        try {
          const appResource: Partial<AppResource> = {
            ...resource,
            authorId: '', // Will be empty as per requirements
            createdAt: new Date().toISOString().slice(0, 10),
          };

          await createAppResource(appResource as AppResource);
          successCount++;
        } catch (error) {
          console.error('Error creating resource:', error);
          errorCount++;
        }
      }

      if (successCount > 0) {
        toast.success(`Successfully imported ${successCount} resources`);
      }
      if (errorCount > 0) {
        toast.error(`Failed to import ${errorCount} resources`);
      }

      if (successCount > 0) {
        setJsonInput('');
      }
    } catch (error) {
      toast.error('An unexpected error occurred during import');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleJsonChange = (value: string) => {
    setJsonInput(value);
    if (value.trim()) {
      validateAndParseJson(value);
    } else {
      setValidationErrors([]);
    }
  };

  return (
    <>
      <AppHeader />
      <div className='w-[100%]'>
        <AdminNavigation />
        <div className='max-w-4xl mx-auto p-6'>
          <h1 className='text-2xl font-bold mb-6'>Bulk Import Resources</h1>

          <div className='mb-6'>
            <p className='text-gray-600 dark:text-gray-400 mb-4'>
              Paste a JSON array of resources to import multiple resources at
              once. Each resource should have: title, url, description
              (optional), type, and tags.
            </p>

            <div className='bg-gray-100 dark:bg-gray-800 p-4 rounded-md mb-4'>
              <h3 className='font-semibold mb-2'>Example JSON format:</h3>
              <pre className='text-sm overflow-x-auto'>
                {`[
  {
    "title": "Example Security Article",
    "url": "https://example.com/article",
    "description": "A comprehensive guide to web security",
    "type": "article",
    "tags": []
  }
]`}
              </pre>
            </div>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className='space-y-4'>
            <div>
              <label className='block text-sm font-medium mb-2'>
                JSON Data
              </label>
              <AppTextarea
                value={jsonInput}
                onChange={(e) => handleJsonChange(e.target.value)}
                placeholder='Paste your JSON array here...'
                rows={15}
                className='font-mono text-sm'
              />
            </div>

            {validationErrors.length > 0 && (
              <div className='bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4'>
                <h4 className='font-semibold text-red-800 dark:text-red-200 mb-2'>
                  Validation Errors:
                </h4>
                <ul className='list-disc list-inside space-y-1'>
                  {validationErrors.map((error, index) => (
                    <li
                      key={index}
                      className='text-red-700 dark:text-red-300 text-sm'
                    >
                      {error}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            <AppSubmitButton isSubmitting={isSubmitting || isProcessing} />
          </form>
        </div>
      </div>
    </>
  );
};
