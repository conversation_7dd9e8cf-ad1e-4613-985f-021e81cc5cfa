import { AppHeader } from '@/components/app-header/app-header';
import { ManageAppResources } from './ManageAppResource/ManageAppResource';
import { ManageAuthors } from './ManageAuthors';
import { ProposalsContent } from './ManageProposals/ManageProposals';
import { ManageTags } from './ManageTags';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useState } from 'react';

export const AdminPage = () => {
  const [tab, setTab] = useState('tags');

  return (
    <>
      <AppHeader />
      <div className='w-[100%]'>
        <Tabs
          defaultValue={tab}
          onValueChange={setTab}
          className='flex flex-col'
        >
          <TabsList className='flex-wrap h-full px-2 m-2'>
            <TabsTrigger
              value='tags'
              className='text-zinc-600 dark:text-zinc-200'
            >
              Tags
            </TabsTrigger>
            <TabsTrigger
              value='authors'
              className='text-zinc-600 dark:text-zinc-200'
            >
              Authors
            </TabsTrigger>
            <TabsTrigger
              value='app-resources'
              className='text-zinc-600 dark:text-zinc-200'
            >
              App resources
            </TabsTrigger>
            <TabsTrigger
              value='proposals'
              className='text-zinc-600 dark:text-zinc-200'
            >
              Proposals
            </TabsTrigger>
          </TabsList>

          <TabsContent value='tags'>
            <ManageTags />
          </TabsContent>
          <TabsContent value='authors'>
            <ManageAuthors />
          </TabsContent>
          <TabsContent value='app-resources'>
            <ManageAppResources />
          </TabsContent>
          <TabsContent value='proposals'>
            <ProposalsContent />
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};
