import { AppConfirm } from '@/components/AppConfirm';
import { AppAuthor } from '../../core.types';
import { EditAuthorModal } from './EditAuthorModal';
import { deleteAuthor, updateAuthor } from '@/api/authors.api';
import { useRootContext } from '@/context/useRootContext';

type ManageAuthorItemProps = {
  author: AppAuthor;
};

export const ManageAuthorItem = ({ author }: ManageAuthorItemProps) => {
  const { setAuthors } = useRootContext();

  const onDeleteAuthorConfirmed = async () => {
    if (author.id === null) return;
    await deleteAuthor(author.id);
    setAuthors((prevAuthors: <AUTHORS>
      prevAuthors.filter((x) => x.id !== author.id),
    );
  };

  const onAuthorEdit = async (values: AppAuthor) => {
    await updateAuthor({ ...values } as any);
    setAuthors((prevAuthors: <AUTHORS>
      prevAuthors.map((author) => {
        if (author.id === values.id) {
          return { ...values };
        }
        return author;
      }),
    );
  };

  return (
    <div className='flex items-center justify-between bg-gray-100 p-4 rounded-md shadow text-black gap-1'>
      <div className='text-lg font-medium flex gap-1 items-center'>
        <img
          src={author.avatarUrl}
          alt='no logo'
          className='rounded-[50%] object-cover w-[30px] h-[30px]'
        />
        <a href={`https://twitter.com/${author.twitter}`}>{author.name}</a>
      </div>
      <div>
        <EditAuthorModal defaultValues={author} onSubmit={onAuthorEdit}>
          <button className='text-blue-500 hover:text-blue-700 mr-2'>
            Edit
          </button>
        </EditAuthorModal>

        <AppConfirm
          onConfirm={onDeleteAuthorConfirmed}
          message='Are you sure to Delete this author?'
        >
          <button className='text-red-500 hover:text-red-700'>Delete</button>
        </AppConfirm>
      </div>
    </div>
  );
};
