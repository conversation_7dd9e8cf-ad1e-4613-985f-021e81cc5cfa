import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';

const adminRoutes = [
  {
    path: '/admin/tags',
    label: 'Tags',
  },
  {
    path: '/admin/authors',
    label: 'Authors',
  },
  {
    path: '/admin/resources',
    label: 'App resources',
  },
  {
    path: '/admin/proposals',
    label: 'Proposals',
  },
  {
    path: '/admin/bulk-import',
    label: 'Bulk Import',
  },
];

export const AdminNavigation = () => {
  const location = useLocation();

  return (
    <nav className='flex flex-wrap gap-2 p-4 border-b border-gray-200 dark:border-gray-700'>
      {adminRoutes.map((route) => (
        <Link
          key={route.path}
          to={route.path}
          className={cn(
            'px-4 py-2 rounded-md text-sm font-medium transition-colors',
            'hover:bg-gray-100 dark:hover:bg-gray-800',
            location.pathname === route.path
              ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
              : 'text-gray-600 dark:text-gray-300'
          )}
        >
          {route.label}
        </Link>
      ))}
    </nav>
  );
};
