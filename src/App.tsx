import { RouterProvider, createBrowserRouter } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { AdminPage } from './admin-page/AdminPage';
import { RootProvider } from './context/RootContext';
import ErrorPage from './error-page/ErrorPage';
import { useEffect, useState } from 'react';
import { useDarkMode } from 'usehooks-ts';
import { SearchFiltersContextProvider } from './search-page/search-filters-context';
import { AppResourcesProvider } from './search-page/app-resources-context';
import { SearchPage } from './search-page/search-page';

const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <SearchFiltersContextProvider>
        <AppResourcesProvider>
          <div className='h-screen flex flex-col'>
            <SearchPage />
          </div>
        </AppResourcesProvider>
      </SearchFiltersContextProvider>
    ),
  },
  {
    path: '/admin',
    element: <AdminPage />,
    errorElement: <ErrorPage />,
  },
]);

function App() {
  const { isDarkMode } = useDarkMode(true);
  const [restricted, setRestricted] = useState(false);

  useEffect(() => {
    fetch('https://ipapi.co/json/')
      .then((response) => response.json())
      .then((data) => {
        console.log(data);
        if (['RU', 'IR', 'KP', 'CN'].includes(data.country)) {
          setRestricted(true);
        }
      })
      .catch((error) => {
        console.log(error);
      });
  }, []);

  useEffect(() => {
    if (isDarkMode) {
      document.documentElement.classList.add('dark');
      document.body.classList.add('bodyDark');
    } else {
      document.documentElement.classList.remove('dark');
      document.body.classList.remove('bodyDark');
    }
  }, [isDarkMode]);

  if (restricted) {
    return (
      <div className='fixed inset-0 flex justify-center items-center'>
        russia is a terrorist state
      </div>
    );
  }

  return (
    <RootProvider>
      <RouterProvider router={router} />
      <ToastContainer />
    </RootProvider>
  );
}

export default App;
