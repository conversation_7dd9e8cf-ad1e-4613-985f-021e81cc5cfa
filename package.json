{"name": "sc-blog-search", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@apollo/client": "^3.8.8", "@hookform/resolvers": "^3.3.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "autoprefixer": "^10.4.16", "class-variance-authority": "^0.7.0", "classnames": "^2.3.2", "clsx": "^2.1.1", "cmdk": "^1.0.0", "core-js": "^3.34.0", "date-fns": "^3.6.0", "dayjs": "^1.11.11", "firebase": "^10.6.0", "graphql": "^16.8.1", "jotai": "^2.8.2", "lodash": "^4.17.21", "lucide-react": "^0.381.0", "postcss": "^8.4.31", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-icons": "^4.11.0", "react-resizable-panels": "^2.0.19", "react-router-dom": "^6.18.0", "react-select": "^5.8.0", "react-toastify": "^9.1.3", "react-tooltip": "^5.25.0", "react-tweet": "^3.1.1", "tailwind": "^4.0.0", "tailwind-merge": "^2.3.0", "tailwindcss": "^3.3.5", "tailwindcss-animate": "^1.0.7", "tailwindcss-filters": "^3.0.0", "usehooks-ts": "^2.9.1", "vite-tsconfig-paths": "^4.3.2", "yup": "^1.3.2"}, "devDependencies": {"@types/lodash": "^4.14.200", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "husky": "^8.0.3", "ts-node": "^10.9.1", "typescript": "^5.0.2", "vite": "^4.4.5"}}